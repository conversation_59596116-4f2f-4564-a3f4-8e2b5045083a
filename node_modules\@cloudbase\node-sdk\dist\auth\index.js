"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.auth = exports.Auth = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const utils_1 = require("../utils/utils");
const code_1 = require("../const/code");
const cloudbase_1 = require("../cloudbase");
const symbol_1 = require("../const/symbol");
const tcbapicaller = __importStar(require("../utils/tcbapirequester"));
const tcbopenapicommonrequester = __importStar(require("../utils/tcbopenapicommonrequester"));
const checkCustomUserIdRegex = /^[a-zA-Z0-9_\-#@~=*(){}[\]:.,<>+]{4,32}$/;
function validateUid(uid) {
    if (typeof uid !== 'string') {
        throw (0, utils_1.E)(Object.assign(Object.assign({}, code_1.ERROR.INVALID_PARAM), { message: 'uid must be a string' }));
    }
    if (!checkCustomUserIdRegex.test(uid)) {
        throw (0, utils_1.E)(Object.assign(Object.assign({}, code_1.ERROR.INVALID_PARAM), { message: `Invalid uid: "${uid}"` }));
    }
}
class Auth {
    constructor(cloudbase) {
        this.cloudbase = cloudbase;
    }
    async getAuthContext(context) {
        const { TCB_UUID, LOGINTYPE, QQ_OPENID, QQ_APPID } = cloudbase_1.CloudBase.getCloudbaseContext(context);
        const result = {
            uid: TCB_UUID,
            loginType: LOGINTYPE
        };
        if (LOGINTYPE === 'QQ-MINI') {
            result.appId = QQ_APPID;
            result.openId = QQ_OPENID;
        }
        return result;
    }
    getClientIP() {
        const { TCB_SOURCE_IP } = cloudbase_1.CloudBase.getCloudbaseContext();
        return TCB_SOURCE_IP || '';
    }
    getUserInfo() {
        const { WX_OPENID, WX_APPID, TCB_UUID, TCB_CUSTOM_USER_ID, TCB_ISANONYMOUS_USER } = cloudbase_1.CloudBase.getCloudbaseContext();
        return {
            openId: WX_OPENID || '',
            appId: WX_APPID || '',
            uid: TCB_UUID || '',
            customUserId: TCB_CUSTOM_USER_ID || '',
            isAnonymous: TCB_ISANONYMOUS_USER === 'true'
        };
    }
    async getEndUserInfo(uid, opts) {
        const { WX_OPENID, WX_APPID, TCB_UUID, TCB_CUSTOM_USER_ID, TCB_ISANONYMOUS_USER } = cloudbase_1.CloudBase.getCloudbaseContext();
        const defaultUserInfo = {
            openId: WX_OPENID || '',
            appId: WX_APPID || '',
            uid: TCB_UUID || '',
            customUserId: TCB_CUSTOM_USER_ID || '',
            isAnonymous: TCB_ISANONYMOUS_USER === 'true'
        };
        if (uid === undefined) {
            return await Promise.resolve({
                userInfo: defaultUserInfo
            });
        }
        validateUid(uid);
        return await tcbapicaller.request({
            config: this.cloudbase.config,
            params: {
                action: 'auth.getUserInfoForAdmin',
                uuid: uid
            },
            method: 'post',
            opts,
            headers: {
                'content-type': 'application/json'
            }
        }).then(result => {
            if (result.code) {
                return result;
            }
            return {
                userInfo: Object.assign(Object.assign({}, defaultUserInfo), result.data),
                requestId: result.requestId
            };
        });
    }
    async queryUserInfo(query, opts) {
        const { uid, platform, platformId } = query;
        return await tcbapicaller.request({
            config: this.cloudbase.config,
            params: {
                action: 'auth.getUserInfoForAdmin',
                uuid: uid,
                platform,
                platformId
            },
            method: 'post',
            opts,
            headers: {
                'content-type': 'application/json'
            }
        }).then(result => {
            if (result.code) {
                return result;
            }
            return {
                userInfo: Object.assign({}, result.data),
                requestId: result.requestId
            };
        });
    }
    async getClientCredential(opts) {
        return await tcbopenapicommonrequester.request({
            config: this.cloudbase.config,
            method: 'POST',
            opts,
            headers: {
                'content-type': 'application/json'
            },
            path: '/auth/v1/token/clientCredential',
            data: {
                grant_type: 'client_credentials'
            }
        }).then(result => {
            return result.body;
        });
    }
    createTicket(uid, options = {}) {
        validateUid(uid);
        const timestamp = new Date().getTime();
        const { TCB_ENV, SCF_NAMESPACE } = cloudbase_1.CloudBase.getCloudbaseContext();
        const { credentials } = this.cloudbase.config;
        /* eslint-disable-next-line */
        const { env_id } = credentials;
        let { envName } = this.cloudbase.config;
        if (!envName) {
            throw (0, utils_1.E)(Object.assign(Object.assign({}, code_1.ERROR.INVALID_PARAM), { message: 'no env in config' }));
        }
        // 检查 credentials 是否包含 env
        if (!env_id) {
            throw (0, utils_1.E)(Object.assign(Object.assign({}, code_1.ERROR.INVALID_PARAM), { message: '当前私钥未包含env_id 信息， 请前往腾讯云云开发控制台，获取自定义登录最新私钥' }));
        }
        // 使用symbol时替换为环境变量内的env
        if (envName === symbol_1.SYMBOL_CURRENT_ENV) {
            envName = TCB_ENV || SCF_NAMESPACE;
        }
        else if (envName === symbol_1.SYMBOL_DEFAULT_ENV) {
            // nothing to do
        }
        // 检查 credentials env 和 init 指定 env 是否一致
        if (env_id && env_id !== envName) {
            throw (0, utils_1.E)(Object.assign(Object.assign({}, code_1.ERROR.INVALID_PARAM), { message: '当前私钥所属环境与 init 指定环境不一致！' }));
        }
        if (!Reflect.has(options, 'allowInsecureKeySizes')) {
            options.allowInsecureKeySizes = true;
        }
        const { refresh = 3600 * 1000, expire = timestamp + 7 * 24 * 60 * 60 * 1000 } = options;
        const token = jsonwebtoken_1.default.sign({
            alg: 'RS256',
            env: envName,
            iat: timestamp,
            exp: timestamp + 10 * 60 * 1000,
            uid,
            refresh,
            expire
        }, credentials.private_key, {
            allowInsecureKeySizes: options.allowInsecureKeySizes === true,
            algorithm: 'RS256'
        });
        return credentials.private_key_id + '/@@/' + token;
    }
}
exports.Auth = Auth;
function auth(cloudbase) {
    return new Auth(cloudbase);
}
exports.auth = auth;
