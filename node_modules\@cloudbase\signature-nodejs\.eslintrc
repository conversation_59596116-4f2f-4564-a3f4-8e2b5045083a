{
  "extends": [
    "@tencent/eslint-config-tencent",
    "@tencent/eslint-config-tencent/ts"
  ],
  "plugins": [
    "typescript"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2020,
    "sourceType": "script"
  },
  "env": {
    "node": true,
    "es6": true,
    "jest": true
  },
  "rules": {
    // "prettier/prettier": [
    //   "error",
    //   {
    //     "tabWidth": 2,
    //     "semi": false,
    //     "singleQuote": true,
    //     "endOfLine": "lf"
    //   },
    //   {
    //     "usePrettierrc": false
    //   }
    // ],
    "typescript/no-unused-vars": "warn",
    "@typescript-eslint/semi": 0,
    "no-unused-vars": "warn",
    "semi": [
      "error",
      "never"
    ],
    "quotes": [
      "error",
      "single",
      {
        "avoidEscape": true
      }
    ],
    "no-extra-semi": 0
  }
}