"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCurrRunEnvTag = exports.checkIsInternalAsync = exports.checkIsInternal = exports.checkIsInTencentCloud = exports.checkIsInSumeru = exports.checkIsInCBR = exports.checkIsInScf = exports.getCloudPlatform = exports.preflightRuntimeCloudPlatform = exports.hasPreflight = void 0;
const metadata_1 = require("./metadata");
const utils_1 = require("./utils");
var CloudPlatform;
(function (CloudPlatform) {
    CloudPlatform["Unknown"] = "";
    CloudPlatform["TencentCloud"] = "tencentcloud";
    CloudPlatform["Other"] = "other";
})(CloudPlatform || (CloudPlatform = {}));
let hasDetected = false;
let cloudPlatform = CloudPlatform.Unknown;
function hasPreflight() {
    return hasDetected;
}
exports.hasPreflight = hasPreflight;
async function preflightRuntimeCloudPlatform() {
    if (hasDetected) {
        return;
    }
    if (await checkIsInternalAsync()) {
        cloudPlatform = CloudPlatform.TencentCloud;
    }
    else {
        cloudPlatform = CloudPlatform.Other;
    }
    hasDetected = true;
}
exports.preflightRuntimeCloudPlatform = preflightRuntimeCloudPlatform;
function getCloudPlatform() {
    return cloudPlatform;
}
exports.getCloudPlatform = getCloudPlatform;
function checkIsInScf() {
    return process.env.TENCENTCLOUD_RUNENV === 'SCF';
}
exports.checkIsInScf = checkIsInScf;
function checkIsInCBR() {
    // CBR = CLOUDBASE_RUN
    return !!process.env.CBR_ENV_ID;
}
exports.checkIsInCBR = checkIsInCBR;
const kSumeruEnvSet = new Set(['formal', 'pre', 'test']);
function checkIsInSumeru() {
    // SUMERU_ENV=formal | test | pre
    return kSumeruEnvSet.has(process.env.SUMERU_ENV);
}
exports.checkIsInSumeru = checkIsInSumeru;
async function checkIsInTencentCloud() {
    return (0, utils_1.isNonEmptyString)(await (0, metadata_1.lookupAppId)());
}
exports.checkIsInTencentCloud = checkIsInTencentCloud;
function checkIsInternal() {
    return checkIsInScf() || checkIsInCBR() || checkIsInSumeru();
}
exports.checkIsInternal = checkIsInternal;
async function checkIsInternalAsync() {
    return checkIsInternal() ? await Promise.resolve(true) : await checkIsInTencentCloud();
}
exports.checkIsInternalAsync = checkIsInternalAsync;
async function getCurrRunEnvTag() {
    if (checkIsInScf()) {
        return 'scf';
    }
    else if (checkIsInCBR()) {
        return 'cbr';
    }
    else if (checkIsInSumeru()) {
        return 'sumeru';
    }
    else if (await checkIsInTencentCloud()) {
        return 'tencentcloud';
    }
    return 'unknown';
}
exports.getCurrRunEnvTag = getCurrRunEnvTag;
