import { SDKRequestInterface } from '@cloudbase/adapter-interface';
/**
 * 基础 Model 类型定义
 */
export interface Model {
    _id?: string;
    createdAt?: number;
    updatedAt?: number;
    owner?: Relation;
    createBy?: Relation;
    updateBy?: Relation;
    _openid?: string;
}
export type EnvType = 'prod' | 'pre';
/**
 * 模型方法的定义
 * =====================================================================
 */
/**
 * 模型操作方法的返回类型定义。
 * @template T 返回数据的类型。
 */
export type MethodResponse<T> = {
    /**
     * 返回的数据。
     */
    data: T;
    /**
     * 请求的唯一标识符。
     */
    requestId?: string;
};
/**
 * 模型操作方法接口定义。
 * @template T 模型字段的类型。
 */
export interface DataModelMethods<T> {
    /**
     * 创建单条数据的方法。
     * @param {Object} params - 包含创建数据的参数对象。
     * @returns {Promise<MethodResponse<CreateResponse<T>>>} 包含创建响应的Promise对象。
     * @example
     * models.<model_name>.create({
     *   data: {
     *     // 模型字段数据
     *   }
     * }).then(({ data }) => {
     *   console.log(data.id); // 输出创建的数据ID
     * });
     */
    create: (params: {
        data: T;
        /**
         * 请求数据的环境类型，默认是 prod，可选值有 prod 和 pre
         */
        envType?: EnvType;
    }) => Promise<MethodResponse<CreateResponse<T>>>;
    /**
     * 创建多条数据的方法。
     * @param {Object} params - 包含创建数据数组的参数对象。
     * @returns {Promise<MethodResponse<CreateManyResponse<T>>>} 包含创建多个响应的Promise对象。
     * @example
     * models.<model_name>.createMany({
     *   data: [
     *     // 模型字段数据数组
     *   ]
     * }).then(({ data }) => {
     *   console.log(data.idList); // 输出创建的数据ID列表
     * });
     */
    createMany: (params: {
        data: T[];
        /**
         * 请求数据的环境类型，默认是 prod，可选值有 prod 和 pre
         */
        envType?: EnvType;
    }) => Promise<MethodResponse<CreateManyResponse<T>>>;
    /**
     * 更新单条数据的方法。
     * @param {Object} params - 包含更新数据和筛选条件的参数对象。
     * @returns {Promise<MethodResponse<UpdateResponse<T>>>} 包含更新响应的Promise对象。
     * @example
     * models.<model_name>.update({
     *   data: {
     *     // 更新的数据字段
     *   },
     *   filter: {
     *     where: {
     *       // 筛选条件
     *     }
     *   }
     * }).then(({ data }) => {
     *   console.log(data.count); // 输出更新的数据条数
     * });
     */
    update: (params: {
        data: T;
        filter: FilterParams<T>;
        /**
         * 请求数据的环境类型，默认是 prod，可选值有 prod 和 pre
         */
        envType?: EnvType;
    }) => Promise<MethodResponse<UpdateResponse<T>>>;
    /**
     * 创建或者更新的方法
     * @param {Object} params - 包含创建或者更新对象以及和筛选条件的参数对象。
     * @returns {Promise<MethodResponse<UpsertResponse<T>>>} 包含更新响应的Promise对象。
     * @example
     * models.<model_name>.upsert({
     *   update: {
     *     // 更新的数据字段
     *   },
     *   create: {
     *     // 创建的数据字段
     *   },
     *   filter: {
     *     where: {
     *       // 筛选条件
     *     }
     *   }
     * }).then(({ data }) => {
     *   console.log(data.count); // 输出更新的数据条数
     * });
     */
    upsert: (params: {
        update: T;
        create: T;
        filter: FilterParams<T>;
        /**
         * 请求数据的环境类型，默认是 prod，可选值有 prod 和 pre
         */
        envType?: EnvType;
    }) => Promise<MethodResponse<UpsertResponse<T>>>;
    /**
     * 更新多条数据的方法。
     * @param {Object} params - 包含更新数据和筛选条件的参数对象。
     * @returns {Promise<MethodResponse<UpdateManyResponse<T>>>} 包含更新多个响应的Promise对象。
     * @example
     * models.<model_name>.updateMany({
     *   data: {
     *     // 更新的数据字段
     *   },
     *   filter: {
     *     where: {
     *       // 筛选条件
     *     }
     *   }
     * }).then(({ data }) => {
     *   console.log(data.count); // 输出更新的数据条数
     * });
     */
    updateMany: (params: {
        data: T;
        filter: FilterParams<T>;
        /**
         * 请求数据的环境类型，默认是 prod，可选值有 prod 和 pre
         */
        envType?: EnvType;
    }) => Promise<MethodResponse<CreateManyResponse<T>>>;
    /**
     * 删除单条数据的方法。
     * @param {Object} params - 包含筛选条件的参数对象。
     * @returns {Promise<MethodResponse<DeleteResponse<T>>>} 包含删除响应的Promise对象。
     * @example
     * models.<model_name>.delete({
     *   filter: {
     *     where: {
     *       // 筛选条件，例如根据ID删除特定记录
     *       _id: {
     *         $eq: "特定ID值"
     *       }
     *     }
     *   }
     * }).then(({ data }) => {
     *   console.log(data.count); // 输出删除的数据条数
     * });
     */
    delete: (params: {
        filter: FilterParams<T>;
        /**
         * 请求数据的环境类型，默认是 prod，可选值有 prod 和 pre
         */
        envType?: EnvType;
    }) => Promise<MethodResponse<DeleteResponse<T>>>;
    /**
     * 删除多条数据的方法。
     * @param {Object} params - 包含筛选条件的参数对象。
     * @returns {Promise<MethodResponse<DeleteManyResponse<T>>>} 包含删除多个响应的Promise对象。
     * @example
     * models.<model_name>.deleteMany({
     *   filter: {
     *     where: {
     *       // 筛选条件，例如删除所有满足特定条件的记录
     *     }
     *   }
     * }).then(({ data }) => {
     *   console.log(data.count); // 输出删除的数据条数
     * });
     */
    deleteMany: (params: {
        filter: FilterParams<T>;
        /**
         * 请求数据的环境类型，默认是 prod，可选值有 prod 和 pre
         */
        envType?: EnvType;
    }) => Promise<MethodResponse<DeleteManyResponse<T>>>;
    /**
     * 获取单条数据的方法。
     * @param {Object} params - 包含筛选条件和选择字段的参数对象。
     * @returns {Promise<MethodResponse<T>>} 包含单个记录的Promise对象。
     * @example
     * models.<model_name>.get({
     *   filter: {
     *     where: {
     *       // 筛选条件
     *     }
     *   },
     *   select: {
     *     $master: true // 选择主表所有字段
     *   }
     * }).then(({ data }) => {
     *   console.log(data); // 输出查询到的数据
     * });
     */
    get: (params: {
        filter: FilterParams<T>;
        select?: SelectParams<T>;
        /**
         * 请求数据的环境类型，默认是 prod，可选值有 prod 和 pre
         */
        envType?: EnvType;
    }) => Promise<MethodResponse<T>>;
    /**
     * 获取多条数据的方法。
     * @param {Object} params - 包含筛选条件、选择字段、分页和排序选项的参数对象。
     * @returns {Promise<MethodResponse<{ records: T[]; total?: number }>>} 包含记录列表和总数的Promise对象。
     * @example
     * models.<model_name>.list({
     *   filter: {
     *     where: {
     *       // 筛选条件
     *     }
     *   },
     *   select: {
     *     $master: true // 选择主表所有字段
     *   },
     *   getCount: true, // 开启用来获取总数
     *   pageSize: 10, // 分页大小
     *   pageNumber: 1, // 当前页码
     *   orderBy: [{ createdAt: 'desc' }] // 排序参数
     * }).then(({ data }) => {
     *   console.log(data.records, data.total); // 输出查询到的数据列表和总数
     * });
     */
    list: (params: {
        /**
         * 过滤条件
         */
        filter?: FilterParams<T>;
        /**
         * 可以指定返回本表或者关联表的字段，如果想查询本表所有字段，请使用 { $master: true }
         */
        select?: SelectParams<T>;
        /**
         * 是否获取 filter 命中条件的查询条数
         */
        getCount?: boolean;
        /**
         * 分页大小，建议指定，如需设置为其它值，需要和pageNo配合使用，两者同时指定才会生效
         */
        pageSize?: number;
        /**
         * 分页数目
         */
        pageNumber?: number;
        /**
         * 排序参数，当前仅支持最多 3 字段排序
         */
        orderBy?: OrderByParams[];
        /**
         * 请求数据的环境类型，默认是 prod，可选值有 prod 和 pre
         */
        envType?: EnvType;
    }) => Promise<MethodResponse<{
        records: T[];
        total?: number;
    }>>;
    /**
     * 运行 sql模板
     * @param {Object} params - 包含模板名、模板参数、环境类型的参数对象。
     * @returns {Promise<MethodResponse<unknown>>} 包含记录列表和总数的Promise对象。
     */
    runSQLTemplate: (params: {
        /**
         * 模板名称
         */
        templateName: string;
        /**
         * 模板参数
         */
        params?: Record<string, any>;
        /**
         * 请求数据的环境类型，默认是 prod，可选值有 prod 和 pre
         */
        envType?: EnvType;
    }) => Promise<MethodResponse<unknown>>;
}
/**
 * 模型方法返回值的定义
 * =====================================================================
 */
/**
 * 数据创建方法的返回类型定义。
 * @template T 模型字段的类型。
 */
export type CreateResponse<T> = {
    id: string;
};
/**
 * 创建多条记录的响应类型定义。
 * @template T 模型字段的类型。
 */
export type CreateManyResponse<T> = {
    /**
     * 创建的记录的ID列表。
     */
    idList: string[];
};
/**
 * 更新操作的响应类型定义。
 * @template T 模型字段的类型。
 */
export type UpdateResponse<T> = {
    /**
     * 更新操作影响的记录数量。
     */
    count: number;
};
/**
 * 创建或者更新的响应类型定义。
 * @template T 模型字段的类型。
 */
export type UpsertResponse<T> = {
    /**
     * 变更的条数，返回非 0 值代表更新成功
     */
    count: number;
    /**
     *  变更的条数，返回非 "" 值代表创建成功
     */
    id: string;
};
/**
 * 删除操作的响应类型定义，用于表示删除操作影响的记录数量。
 * @template T 模型字段的类型。
 */
export type DeleteResponse<T> = {
    /**
     * 删除操作影响的记录数量。
     * 如果count为0，表示没有记录被删除；
     * 如果count大于0，表示有相应数量的记录被成功删除。
     */
    count: number;
};
/**
 * 删除多条记录的响应类型定义，与更新操作的响应类型相同。
 * @template T 模型字段的类型。
 */
export type DeleteManyResponse<T> = UpdateResponse<T>;
/**
 * 数据列表方法的返回类型定义。
 * @template T 模型字段的类型。
 */
export type ListResponse<T> = MethodResponse<{
    records: T[];
    total?: number;
}>;
/**
 * list 方法参数定义。
 * @template T 模型字段的类型。
 */
export type ListParams<T> = {
    filter?: FilterParams<T>;
    select?: SelectParams<T> | {
        $master: boolean;
    };
    getCount?: boolean;
    pageSize?: number;
    pageNumber?: number;
    orderBy?: OrderByParams[];
    relateWhere?: any;
};
/**
 * 过滤条件项的类型定义。
 * =====================================================================
 */
/**
 * 定义过滤参数的类型。
 *
 * @template T 表示模型字段的类型。
 * @example
 * {
    relateWhere: {
      comments: {
        where: {
          comment: {
            $nempty: true,
          },
        },
      },
    },
    where: {},
  }
 */
export type FilterParams<T> = {
    /**
     * 基础查询
     */
    where?: FilterConditionItem<T> | FilterObject<T>;
    /**
     * 关联关系查询
     */
    relateWhere?: {
        [K in RelationKeys<T>]?: {
            where: FilterConditionItem<T[K] extends Model[] ? T[K][0] : T[K]> | FilterObject<T[K]>;
        };
    };
};
/**
 * 定义过滤参数的类型。
 *
 * @template T 表示模型字段的类型。
 * 此类型定义允许使用复杂的查询条件来过滤数据。
 *
 * @example <caption>示例 1: 使用`$and`运算符来组合多个条件。</caption>
 * {
 *   "$and": [
 *     {
 *       "title": {
 *         "$eq": "hello"
 *       }
 *     }
 *   ]
 * }
 *
 * @example <caption>示例 2: 使用`$or`运算符来组合多个条件，其中包含一个`$and`子条件。</caption>
 * {
 *   "$or": [
 *     {
 *       "$and": [
 *         {
 *           "title": {
 *             "$eq": "hello"
 *           }
 *         },
 *         {
 *           "body": {
 *             "$neq": "world"
 *           }
 *         }
 *       ]
 *     },
 *     {
 *       "createdBy": {
 *         "$eq": "xxx"
 *       }
 *     }
 *   ]
 * }
 */
export type FilterObject<T> = {
    [operator in LogicalOperator]?: FilterConditionItem<T>[] | FilterObject<T>;
};
/**
 * 过滤参数类型定义。
 *
 * 此类型定义允许对模型字段进行条件过滤，支持复杂的查询操作。
 *
 * @template T 模型字段的类型。
 *
 * @example
 * 示例 1: 使用`$and`运算符来组合条件，确保所有条件都满足。
 * ```json
 * {
 *   "$and": [
 *     {
 *       "key": {
 *         "$eq": "val"
 *       }
 *     }
 *   ]
 * }
 * ```
 *
 * @example
 * 示例 2: 使用`$and`运算符来组合条件，其中包含`$in`运算符来检查数组包含性。
 * ```json
 * {
 *   "$and": [
 *     {
 *       "key1": {
 *         "$in": [
 *           "foo",
 *           "bar"
 *         ]
 *       }
 *     },
 *     {
 *       "key2": {
 *         "$in": [
 *           1,
 *           2
 *         ]
 *       }
 *     }
 *   ]
 * }
 * ```
 */
export type FilterConditionItem<T> = {
    [key in keyof T]?: FilterCondition;
};
/**
 * 复杂查询条件类型定义
 *
 * @example
 * {
 *  "$eq": "val"
 * }
 */
export type FilterCondition = {
    [key in ComparisonOperator]?: any;
};
/**
 * 比较运算符类型定义，包括基础和特殊运算符。
 * @example
 * $eq: 等于
 */
export type ComparisonOperator = BasicComparisonOperator | SpecialComparisonOperator;
/**
 * 排序参数结构定义。
 *
 * @example
 * {
  "createdAt": "asc",
 }
 */
export type OrderByParams = {
    [key: string]: 'asc' | 'desc';
};
/**
 * 选择参数结构定义，用于指定查询时返回的字段。
 * @template T 模型字段的类型。
 * @example
 * {
   "key1": true,
 }
 *
 * @example
 * {
  $master: true,
 }
 *
 * @example
 * {
  $master: true,
  comments: {
   comment: true,
  }
 }
 */
export type SelectParams<T> = {
    [K in keyof T]?: T[K] extends Array<infer U> | undefined ? SelectParams<U> | boolean : T[K] extends object | undefined ? SelectParams<T[K]> | boolean : boolean;
} & {
    $master?: boolean;
};
/**
 * 基础比较运算符类型定义。
 */
export type BasicComparisonOperator = '$eq' | '$neq' | '$gt' | '$gte' | '$lt' | '$lte' | '$in' | '$nin';
/**
 * 特殊比较运算符类型定义
 */
export type SpecialComparisonOperator = '$search' | '$nsearch' | '$empty' | '$nempty';
/**
 * 逻辑运算符类型定义。
 */
export type LogicalOperator = '$and' | '$or';
export type RelationField<T> = T extends {
    [key: string]: infer U;
} ? U : never;
/**
 * @hidden
 */
type RelationKeys<T> = T extends any ? {
    [K in keyof T]: T[K] extends Model | Model[] ? K : never;
}[keyof T] : never;
/**
 * 关联关系类型定义。
 * @hidden
 */
export type Relation = string;
export interface SQLCommandParams {
    /**
     * 超时时间，默认是 5s，最大不超过 15s
     */
    timeout?: number;
    /**
     * 可填写写数据连接名称
     *
     * 连接外部数据库时，需要填写数据库连接名称, 可在云后台的数据库管理中创建和查看数据库连接
     * 如果不填，则会使用默认的数据库连接内置的 Mysql 连接。
     *
     */
    dbLinkName?: string;
}
/**
 * 运行原生SQL的ORM客户端接口
 */
export interface OrmRawQueryClient {
    $runSQL?: (
    /**
     * sql 语句
     */
    sql: string, 
    /**
     * sql 模版变量
     */
    params?: Record<string, any>, 
    /**
     * 配置
     */
    config?: SQLCommandParams) => Promise<MethodResponse<{
        executeResultList: Record<string, any>[];
        total: number;
        backendExecute: string;
    }>>;
    $runSQLRaw?: (
    /**
     * sql 语句
     */
    sql: string, 
    /**
     * 配置
     */
    config?: SQLCommandParams) => Promise<MethodResponse<{
        executeResultList: Record<string, any>[];
        total: number;
        backendExecute: string;
    }>>;
}
/**
 * ORM客户端接口，包含不同模型名称到其操作方法的映射。
 * @hidden
 */
export interface OrmClient {
    [modelName: string]: DataModelMethods<any>;
}
/**
 * 调用数据源参数的结构定义，用于封装调用云函数时所需的参数。
 * @hidden
 */
export interface CallDataSourceParams {
    /**
     * 数据源的名称，标识调用的数据源。
     */
    dataSourceName?: string;
    /**
     * 方法名称，标识要调用的数据源中的具体方法。
     */
    methodName: string;
    /**
     * 传递给数据源方法的参数，可以是任意形式的键值对。
     */
    params: Record<string, any>;
    /**
     * 真实的方法名称，可能在某些情况下与`methodName`不同。
     */
    realMethodName: string;
    /**
     * 调用函数的方法，用于执行实际的云函数调用。
     */
    callFunction: CallFunction;
    /**
     * 环境类型，标识当前的环境类型，可选值为`prod`和`pre`。
     */
    envType?: EnvType;
    /**
     * @deprecated 使用 dataSourceName 替代。
     */
    mode?: string;
}
/**
 * 调用runMySQLCoommand 参数的结构定义，用于封装调用云函数时所需的参数。
 * @hidden
 */
export interface RunMysqlCommandParams {
    sql: string;
    params?: Record<string, any>;
    callFunction: CallFunction;
    config?: any;
    unsafe?: boolean;
}
/**
 * 云函数调用接口，包含调用函数和认证信息。
 * @hidden
 */
export type CloudBaseInstance = {
    callFunction: CallFunction;
    auth: any;
};
/**
 * 扩展的云实例接口，扩展了云函数调用接口并包含ORM客户端。
 * @hidden
 */
export interface ExtendedCloudBaseInstance extends CloudBaseInstance {
    models: OrmClient & OrmRawQueryClient;
}
/**
 * 云函数调用方法定义。
 * @hidden
 * @param args - 包含函数名称、数据源名称、方法名称和参数的对象。
 * @returns 返回一个Promise，解析为任意类型。
 */
export type CallFunction = (args: {
    name: string;
    data: Record<string, any>;
}) => Promise<any>;
export type ModelFetch = NonNullable<SDKRequestInterface['fetch']>;
export {};
