"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateTracingInfo = void 0;
const cloudbase_1 = require("../cloudbase");
let seqNum = 0;
function getSeqNum() {
    return ++seqNum;
}
function generateEventId() {
    return Date.now().toString(16) + '_' + getSeqNum().toString(16);
}
const generateTracingInfo = (id) => {
    const { TCB_SEQID = '', TCB_TRACELOG } = cloudbase_1.CloudBase.getCloudbaseContext();
    const eventId = generateEventId();
    const seqId = id
        ? `${id}-${eventId}`
        : (TCB_SEQID ? `${TCB_SEQID}-${eventId}` : eventId);
    return { eventId, seqId, trace: TCB_TRACELOG };
};
exports.generateTracingInfo = generateTracingInfo;
