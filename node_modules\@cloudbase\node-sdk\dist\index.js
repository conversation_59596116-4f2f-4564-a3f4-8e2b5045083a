"use strict";
const cloudbase_1 = require("./cloudbase");
const symbol_1 = require("./const/symbol");
const utils_1 = require("./utils/utils");
const request_1 = require("./utils/request");
const version_1 = require("./utils/version");
module.exports = {
    version: version_1.version,
    SYMBOL_CURRENT_ENV: symbol_1.SYMBOL_CURRENT_ENV,
    SYMBOL_DEFAULT_ENV: symbol_1.SYMBOL_DEFAULT_ENV,
    init: (config) => {
        return new cloudbase_1.CloudBase(config);
    },
    parseContext: (context) => {
        return cloudbase_1.CloudBase.parseContext(context);
    },
    getCloudbaseContext: (context) => {
        return cloudbase_1.CloudBase.getCloudbaseContext(context);
    },
    request: request_1.extraRequest,
    setThrowOnCode: utils_1.setThrowOnCode
};
