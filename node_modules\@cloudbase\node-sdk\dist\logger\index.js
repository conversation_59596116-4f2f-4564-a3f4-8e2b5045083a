"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.Logger = void 0;
const utils_1 = require("../utils/utils");
const code_1 = require("../const/code");
const cloudbase_1 = require("../cloudbase");
class Logger {
    constructor() {
        const { _SCF_TCB_LOG } = cloudbase_1.CloudBase.getCloudbaseContext();
        this.src = 'app';
        this.isSupportClsReport = true;
        if (`${_SCF_TCB_LOG}` !== '1') {
            this.isSupportClsReport = false;
        }
        else if (!console.__baseLog__) {
            this.isSupportClsReport = false;
        }
        if (!this.isSupportClsReport) {
            console.warn('[TCB][WARN] 请检查您是否在本地环境 或者 未开通高级日志功能，当前环境下无法上报cls日志，默认使用 console');
        }
    }
    transformMsg(logMsg) {
        // 目前 logMsg 只支持字符串 value 且不支持多级, 加一层转换处理
        let realMsg = {};
        realMsg = Object.assign(Object.assign({}, realMsg), logMsg);
        return realMsg;
    }
    baseLog(logMsg, logLevel) {
        if (Object.prototype.toString.call(logMsg).slice(8, -1) !== 'Object') {
            throw (0, utils_1.E)(Object.assign(Object.assign({}, code_1.ERROR.INVALID_PARAM), { message: 'log msg must be an object' }));
        }
        const msgContent = this.transformMsg(logMsg);
        if (this.isSupportClsReport) {
            ;
            console.__baseLog__(msgContent, logLevel);
        }
        else {
            if (console[logLevel]) {
                console[logLevel](msgContent);
            }
        }
    }
    log(logMsg) {
        this.baseLog(logMsg, 'log');
    }
    info(logMsg) {
        this.baseLog(logMsg, 'info');
    }
    warn(logMsg) {
        this.baseLog(logMsg, 'warn');
    }
    error(logMsg) {
        this.baseLog(logMsg, 'error');
    }
}
exports.Logger = Logger;
function logger() {
    return new Logger();
}
exports.logger = logger;
